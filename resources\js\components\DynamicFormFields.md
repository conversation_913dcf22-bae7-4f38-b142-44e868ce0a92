# DynamicFormFields Component

## Mô tả
Component `DynamicFormFields` là một component dùng chung để render các form field động dựa trên cấu hình từ backend. Component này được tách ra từ `JobAdd.vue` để có thể tái sử dụng ở nhiều nơi khác.

## Tính năng
- Render các loại field khác nhau: TEXT, TEXTAREA, NUMBER, DATE, TIME, MULTISELECT, USER, DEPARTMENT, FILEUPLOAD, FORMULA, TABLE, OBJECTSYSTEM
- Validation tự động dựa trên cấu hình field
- Xử lý file upload
- Hỗ trợ table với các field con
- Tính toán công thức tự động
- Emit dữ liệu về parent component

## Props

### dataFormFields
- **Type**: `Array`
- **Default**: `[]`
- **<PERSON><PERSON> tả**: Mảng các field configuration từ backend

### initialFormData
- **Type**: `Object`
- **Default**: `{}`
- **Mô tả**: Dữ liệu khởi tạo cho form

## Events

### update:formData
- **Payload**: `Object`
- **Mô tả**: Emit khi dữ liệu form thay đổi

### update:itemChildrens
- **Payload**: `Object`
- **Mô tả**: Emit khi dữ liệu table children thay đổi

### update:schema
- **Payload**: `Object`
- **Mô tả**: Emit validation schema cho parent component

## Cách sử dụng

```vue
<template>
  <DynamicFormFields 
    :dataFormFields="formFields"
    :initialFormData="formData"
    @update:formData="handleFormDataUpdate"
    @update:itemChildrens="handleItemChildrensUpdate"
    @update:schema="handleSchemaUpdate"
  />
</template>

<script>
import DynamicFormFields from '@/components/DynamicFormFields.vue'

export default {
  components: {
    DynamicFormFields
  },
  data() {
    return {
      formFields: [], // Từ API
      formData: {},
      itemChildrens: {},
      validationSchema: null
    }
  },
  methods: {
    handleFormDataUpdate(newFormData) {
      this.formData = newFormData
    },
    handleItemChildrensUpdate(newItemChildrens) {
      this.itemChildrens = newItemChildrens
    },
    handleSchemaUpdate(newSchema) {
      this.validationSchema = newSchema
    }
  }
}
</script>
```

## Các loại field được hỗ trợ

1. **TEXT/TEXTAREA/NUMBER/DATE/TIME**: Các field input cơ bản
2. **MULTISELECT**: Dropdown với single/multiple selection
3. **USER**: Select user với search
4. **DEPARTMENT**: Select department
5. **FILEUPLOAD**: Upload file với validation
6. **FORMULA**: Field tính toán tự động
7. **TABLE**: Bảng với các field con
8. **OBJECTSYSTEM**: Select từ system object với sub columns

## Validation
Component tự động tạo validation schema dựa trên cấu hình field và emit về parent component để sử dụng với vee-validate.

## Dependencies
- Vue 3
- vee-validate
- yup
- @vueform/multiselect
- vue-filepond
- mathjs (cho formula calculation)
