import { mount } from '@vue/test-utils'
import { describe, it, expect, beforeEach } from 'vitest'
import DynamicFormFields from '../DynamicFormFields.vue'

describe('DynamicFormFields', () => {
  let wrapper

  const mockDataFormFields = [
    {
      id: 1,
      type: 'VARCHAR',
      display_name: 'Test Field',
      display_name_en: 'Test Field',
      placeholder: 'Enter test value',
      placeholder_en: 'Enter test value',
      keyword: 'test_field',
      required: 1,
      column_width: 6,
      order: 1,
      parent_id: null,
      children: [],
      default_value: null,
      not_edit: 0,
      min_equal: null,
      max_equal: null,
      multiple: 0,
      options: null
    }
  ]

  beforeEach(() => {
    wrapper = mount(DynamicFormFields, {
      props: {
        dataFormFields: mockDataFormFields,
        initialFormData: {}
      },
      global: {
        mocks: {
          $t: (key) => key
        }
      }
    })
  })

  it('renders correctly', () => {
    expect(wrapper.exists()).toBe(true)
  })

  it('processes form fields correctly', () => {
    expect(wrapper.vm.sortedFormFields).toBeDefined()
    expect(wrapper.vm.sortedFormFields.length).toBeGreaterThan(0)
  })

  it('emits formData updates', async () => {
    wrapper.vm.state.formData.test_field = 'test value'
    await wrapper.vm.$nextTick()
    
    expect(wrapper.emitted('update:formData')).toBeTruthy()
  })

  it('handles different field types', () => {
    const textField = wrapper.vm.sortedFormFields.find(field => field.type === 'text')
    expect(textField).toBeDefined()
    expect(textField.name).toBe('test_field')
  })
})
